import express, { Router } from "express";

const routes: Router = express.Router();

import categoryRoute from "./category.routes";
import foodAttributesRoute from "./foodAttributes.routes";
import recipeMeasureRoute from "./recipeMeasure.routes";
import ingredientsRoute from "./ingredients.routes";
import settingsRoute from "./settings.routes";
import dashboardRoute from "./dashboard.routes";
import analyticsRoute from "./analytics.routes";
import recipeRoute from "./recipe.routes";

// Category routes (with integrated icon upload)
routes.use("/category", categoryRoute);

// Food attributes routes (with integrated icon upload)
routes.use("/food-attributes", foodAttributesRoute);

// Recipe measure units routes (with integrated icon upload)
routes.use("/recipe-measures", recipeMeasureRoute);

// Ingredients routes
routes.use("/ingredients", ingredientsRoute);

// Settings routes
routes.use("/settings", settingsRoute);

// Dashboard routes
routes.use("/dashboard", dashboardRoute);

// Analytics routes (private)
routes.use("/analytics", analyticsRoute);
// Recipe routes
routes.use("/recipes", recipeRoute);

export default routes;
