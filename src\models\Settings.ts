import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum SettingType {
  STRING = "string",
  NUMBER = "number",
  BOOLEAN = "boolean",
  JSON = "json",
  ARRAY = "array",
}

export enum SettingCategory {
  RECIPE = "recipe",
  DASHBOARD = "dashboard",
  PUBLIC = "public",
  ANALYTICS = "analytics",
  SYSTEM = "system",
}

interface SettingsAttributes {
  id?: number;
  setting_key: string;
  setting_value: any;
  setting_type: SettingType;
  setting_category: SettingCategory;
  setting_description?: string;
  is_system_setting: boolean;
  organization_id?: string;
  created_by: number;
  updated_by: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export class Settings
  extends Model<SettingsAttributes, never>
  implements SettingsAttributes
{
  id!: number;
  setting_key!: string;
  setting_value!: any;
  setting_type!: SettingType;
  setting_category!: SettingCategory;
  setting_description?: string;
  is_system_setting!: boolean;
  organization_id?: string;
  created_by!: number;
  updated_by!: number;
  createdAt!: Date;
  updatedAt!: Date;

  // Helper methods for type-safe value access
  getStringValue(): string {
    return this.setting_type === SettingType.STRING
      ? this.setting_value
      : String(this.setting_value);
  }

  getBooleanValue(): boolean {
    // Ensure we return a proper boolean regardless of stored representation
    if (typeof this.setting_value === "boolean") {
      return this.setting_value;
    }

    if (typeof this.setting_value === "string") {
      const trimmed = this.setting_value.trim().toLowerCase();
      if (trimmed === "true") return true;
      if (trimmed === "false") return false;
    }

    if (typeof this.setting_value === "number") {
      return this.setting_value !== 0;
    }

    // Fallback conversion
    return Boolean(this.setting_value);
  }

  getNumberValue(): number {
    return this.setting_type === SettingType.NUMBER
      ? this.setting_value
      : Number(this.setting_value);
  }

  getJsonValue(): any {
    return this.setting_type === SettingType.JSON
      ? this.setting_value
      : JSON.parse(this.setting_value);
  }

  getArrayValue(): any[] {
    return this.setting_type === SettingType.ARRAY
      ? this.setting_value
      : JSON.parse(this.setting_value);
  }
}

Settings.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    setting_key: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: "unique_org_setting",
    },
    setting_value: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    setting_type: {
      type: DataTypes.ENUM(Object.values(SettingType)),
      allowNull: false,
      defaultValue: SettingType.STRING,
    },
    setting_category: {
      type: DataTypes.ENUM(Object.values(SettingCategory)),
      allowNull: false,
    },
    setting_description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    is_system_setting: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
      unique: "unique_org_setting",
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "Settings",
    tableName: "mo_recipe_settings",
    timestamps: true,
  }
);

export default Settings;
