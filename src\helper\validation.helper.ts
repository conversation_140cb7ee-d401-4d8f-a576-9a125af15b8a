import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';

/**
 * Enhanced input validation utility
 */
export class ValidationHelper {
  /**
   * Check validation results and return errors if any
   */
  static checkValidation(req: Request, res: Response, next: NextFunction) {
    // Since we're using celebrate for validation, this is a placeholder
    // The actual validation is handled by celebrate middleware
    next();
  }

  /**
   * Validate required string field (basic validation)
   */
  static validateRequiredString(value: any, field: string, minLength: number = 1, maxLength: number = 255): string | null {
    if (!value || typeof value !== 'string') {
      return `${field} is required`;
    }

    const trimmed = value.trim();
    if (trimmed.length < minLength || trimmed.length > maxLength) {
      return `${field} must be between ${minLength} and ${maxLength} characters`;
    }

    return null;
  }

  /**
   * Validate optional string field (basic validation)
   */
  static validateOptionalString(value: any, field: string, maxLength: number = 255): string | null {
    if (value === null || value === undefined || value === '') {
      return null; // Optional field
    }

    if (typeof value !== 'string') {
      return `${field} must be a string`;
    }

    if (value.trim().length > maxLength) {
      return `${field} must not exceed ${maxLength} characters`;
    }

    return null;
  }

  /**
   * Validate email field (basic validation)
   */
  static validateEmail(value: any, field: string = 'email'): string | null {
    if (!value || typeof value !== 'string') {
      return `${field} is required`;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value.trim())) {
      return 'Please provide a valid email address';
    }

    return null;
  }

  /**
   * Validate numeric field (basic validation)
   */
  static validateNumber(value: any, field: string, min?: number, max?: number): string | null {
    const num = Number(value);
    if (isNaN(num)) {
      return `${field} must be a valid number`;
    }

    if (min !== undefined && num < min) {
      return `${field} must be at least ${min}`;
    }

    if (max !== undefined && num > max) {
      return `${field} must not exceed ${max}`;
    }

    return null;
  }

  /**
   * Validate positive integer (basic validation)
   */
  static validatePositiveInteger(value: any, field: string): string | null {
    const num = Number(value);
    if (isNaN(num) || !Number.isInteger(num) || num <= 0) {
      return `${field} must be a positive integer`;
    }

    return null;
  }

  /**
   * Validate array field (basic validation)
   */
  static validateArray(value: any, field: string, minLength: number = 0, maxLength?: number): string | null {
    if (!Array.isArray(value)) {
      return `${field} must be an array`;
    }

    if (value.length < minLength) {
      return `${field} must have at least ${minLength} items`;
    }

    if (maxLength !== undefined && value.length > maxLength) {
      return `${field} must not exceed ${maxLength} items`;
    }

    return null;
  }

  /**
   * Sanitize HTML content
   */
  static sanitizeHtml(value: string): string {
    if (!value) return value;
    
    // Basic HTML sanitization - remove script tags and dangerous attributes
    return value
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  }

  /**
   * Validate and sanitize user input
   */
  static sanitizeInput(input: any): any {
    if (typeof input === 'string') {
      return this.sanitizeHtml(input);
    }
    
    if (Array.isArray(input)) {
      return input.map(item => this.sanitizeInput(item));
    }
    
    if (typeof input === 'object' && input !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(input)) {
        sanitized[key] = this.sanitizeInput(value);
      }
      return sanitized;
    }
    
    return input;
  }

  /**
   * Validate file upload
   */
  static validateFileUpload(field: string, allowedTypes: string[] = [], maxSize?: number) {
    return (req: Request, res: Response, next: NextFunction) => {
      const files = req.files as any;
      
      if (!files || !files[field]) {
        return next(); // Optional file
      }
      
      const file = Array.isArray(files[field]) ? files[field][0] : files[field];
      
      // Check file type
      if (allowedTypes.length > 0 && !allowedTypes.includes(file.mimetype)) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: `Invalid file type. Allowed types: ${allowedTypes.join(', ')}`
        });
      }
      
      // Check file size
      if (maxSize && file.size > maxSize) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: `File size too large. Maximum size: ${maxSize} bytes`
        });
      }
      
      next();
    };
  }

  /**
   * Validate organization access (uses existing isDefaultAccess function)
   */
  static validateOrganizationAccess() {
    return async (req: Request, res: Response, next: NextFunction) => {
      const user = (req as any).user;

      if (!user) {
        return res.status(StatusCodes.UNAUTHORIZED).json({
          status: false,
          message: 'Authentication required'
        });
      }

      // Use existing role-based access check
      const { isDefaultAccess } = await import('../helper/common');
      const hasDefaultAccess = await isDefaultAccess(user.id);

      if (!user.organization_id && !hasDefaultAccess) {
        return res.status(StatusCodes.UNAUTHORIZED).json({
          status: false,
          message: 'Organization access required'
        });
      }

      next();
    };
  }

  /**
   * Validate admin access (uses existing role-based system)
   */
  static validateAdminAccess() {
    return async (req: Request, res: Response, next: NextFunction) => {
      const user = (req as any).user;

      if (!user) {
        return res.status(StatusCodes.UNAUTHORIZED).json({
          status: false,
          message: 'Authentication required'
        });
      }

      // Use existing role-based access check
      const { isDefaultAccess } = await import('../helper/common');
      const hasDefaultAccess = await isDefaultAccess(user.id);

      if (!hasDefaultAccess) {
        return res.status(StatusCodes.FORBIDDEN).json({
          status: false,
          message: 'Admin access required'
        });
      }

      next();
    };
  }

  /**
   * Get effective organization ID for queries (async to check roles properly)
   * Admin users can specify organization_id in query params or get all data
   * Regular users are restricted to their organization
   */
  static async getEffectiveOrganizationId(user: any, queryOrganizationId?: string): Promise<string | null | undefined> {
    // Use existing role-based access check
    const { isDefaultAccess } = await import('../helper/common');
    const hasDefaultAccess = await isDefaultAccess(user.id);

    if (hasDefaultAccess) {
      // Admin can specify organization_id in query, or get all data if not specified
      if (queryOrganizationId !== undefined) {
        return queryOrganizationId === 'null' || queryOrganizationId === '' ? null : queryOrganizationId;
      }
      // If no organization specified in query, return undefined to get all data
      return undefined;
    } else {
      // Regular users are restricted to their organization
      return user.organization_id;
    }
  }

  /**
   * Check if user has admin access (async to check roles properly)
   */
  static async isAdminUser(user: any): Promise<boolean> {
    // Use existing role-based access check
    const { isDefaultAccess } = await import('../helper/common');
    return await isDefaultAccess(user.id);
  }

  /**
   * Validate organization access for analytics endpoints
   */
  static validateAnalyticsAccess() {
    return async (req: Request, res: Response, next: NextFunction) => {
      const user = (req as any).user;

      if (!user) {
        return res.status(StatusCodes.UNAUTHORIZED).json({
          status: false,
          message: 'Authentication required'
        });
      }

      // Use existing role-based access check
      const { isDefaultAccess } = await import('../helper/common');
      const hasDefaultAccess = await isDefaultAccess(user.id);

      // For analytics, either admin or user with organization_id
      if (!hasDefaultAccess && !user.organization_id) {
        return res.status(StatusCodes.UNAUTHORIZED).json({
          status: false,
          message: 'Organization access required for analytics'
        });
      }

      next();
    };
  }

  /**
   * Rate limiting validation
   */
  static validateRateLimit(maxRequests: number = 100, windowMs: number = 15 * 60 * 1000) {
    const requests = new Map<string, { count: number; resetTime: number }>();
    
    return (req: Request, res: Response, next: NextFunction) => {
      const identifier = req.ip || 'unknown';
      const now = Date.now();
      const windowStart = now - windowMs;
      
      // Clean old entries
      for (const [key, value] of requests.entries()) {
        if (value.resetTime < windowStart) {
          requests.delete(key);
        }
      }
      
      // Check current request count
      const current = requests.get(identifier);
      if (!current) {
        requests.set(identifier, { count: 1, resetTime: now });
        return next();
      }
      
      if (current.count >= maxRequests) {
        return res.status(StatusCodes.TOO_MANY_REQUESTS).json({
          status: false,
          message: 'Too many requests. Please try again later.',
          retryAfter: Math.ceil((current.resetTime + windowMs - now) / 1000)
        });
      }
      
      current.count++;
      next();
    };
  }
}

/**
 * Common validation utilities for different entities
 */
export const CommonValidations = {
  /**
   * Validate recipe data
   */
  validateRecipeData(data: any): string[] {
    const errors: string[] = [];

    const titleError = ValidationHelper.validateRequiredString(data.recipe_title, 'recipe_title', 2, 100);
    if (titleError) errors.push(titleError);

    const descError = ValidationHelper.validateOptionalString(data.recipe_description, 'recipe_description', 1000);
    if (descError) errors.push(descError);

    const prepTimeError = ValidationHelper.validateNumber(data.recipe_preparation_time, 'recipe_preparation_time', 0, 1440);
    if (prepTimeError) errors.push(prepTimeError);

    const cookTimeError = ValidationHelper.validateNumber(data.recipe_cook_time, 'recipe_cook_time', 0, 1440);
    if (cookTimeError) errors.push(cookTimeError);

    return errors;
  },

  /**
   * Validate ingredient data
   */
  validateIngredientData(data: any): string[] {
    const errors: string[] = [];

    const nameError = ValidationHelper.validateRequiredString(data.ingredient_name, 'ingredient_name', 2, 100);
    if (nameError) errors.push(nameError);

    const descError = ValidationHelper.validateOptionalString(data.ingredient_description, 'ingredient_description', 500);
    if (descError) errors.push(descError);

    const costError = ValidationHelper.validateNumber(data.cost_per_unit, 'cost_per_unit', 0);
    if (costError) errors.push(costError);

    const unitError = ValidationHelper.validatePositiveInteger(data.unit_of_measure, 'unit_of_measure');
    if (unitError) errors.push(unitError);

    return errors;
  },

  /**
   * Validate category data
   */
  validateCategoryData(data: any): string[] {
    const errors: string[] = [];

    const nameError = ValidationHelper.validateRequiredString(data.category_name, 'category_name', 2, 50);
    if (nameError) errors.push(nameError);

    const descError = ValidationHelper.validateOptionalString(data.category_description, 'category_description', 200);
    if (descError) errors.push(descError);

    return errors;
  }
};
