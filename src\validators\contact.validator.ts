import { celebrate, Joi, Segments } from "celebrate";

const createContactUsValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      name: Joi.string().max(100).required(),
      email: Joi.string().email().max(255).required(),
      mobile: Joi.alternatives()
        .try(Joi.string().max(20), Joi.string().allow(null, ""))
        .optional(),
      message: Joi.string().max(1000).required(),
      subject: Joi.string().max(200).optional(),
      recipe_id: Joi.alternatives()
        .try(
          Joi.number().integer().min(1),
          Joi.string()
            .pattern(/^\d+$/)
            .custom((value) => parseInt(value))
        )
        .allow(null)
        .optional(),
    }),
  });

const updateContactUsValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      name: Joi.string().max(100).optional(),
      email: Joi.string().email().max(255).optional(),
      mobile: Joi.string().max(20).allow(null, "").optional(),
      message: Joi.string().optional(),
      recipe_id: Joi.number().integer().allow(null).optional(),
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const deleteContactUsValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getContactUsValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getContactUsListValidator = () =>
  celebrate({
    [Segments.QUERY]: Joi.object().keys({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(10),
      search: Joi.string().max(100).optional(),
      start_date: Joi.date().iso().optional(),
      end_date: Joi.date().iso().optional(),
      sort_by: Joi.string()
        .valid("name", "email", "created_at", "updated_at")
        .default("created_at"),
      sort_order: Joi.string().valid("asc", "desc").default("desc"),
    }),
  });

const exportContactUsValidator = () =>
  celebrate({
    [Segments.QUERY]: Joi.object().keys({
      format: Joi.string().valid("excel", "csv").default("excel"),
      search: Joi.string().max(100).optional(),
      recipe_id: Joi.number().integer().min(1).optional(),
      start_date: Joi.date().iso().optional(),
      end_date: Joi.date().iso().min(Joi.ref("start_date")).optional(),
    }),
  });

const bulkDeleteContactUsValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        contact_ids: Joi.array()
          .items(Joi.number().integer().min(1))
          .optional(),
        delete_all: Joi.boolean().default(false),
        filters: Joi.object()
          .keys({
            search: Joi.string().max(100).optional(),
            recipe_id: Joi.number().integer().min(1).optional(),
            start_date: Joi.date().iso().optional(),
            end_date: Joi.date().iso().min(Joi.ref("start_date")).optional(),
          })
          .optional(),
      })
      .custom((value, helpers) => {
        // Validate that either contact_ids is provided or delete_all is true
        if (
          !value.delete_all &&
          (!value.contact_ids || value.contact_ids.length === 0)
        ) {
          return helpers.error("any.required");
        }
        return value;
      })
      .messages({
        "any.required":
          "Either provide contact_ids array or set delete_all to true with filters",
      }),
  });

export default {
  createContactUsValidator,
  updateContactUsValidator,
  deleteContactUsValidator,
  getContactUsValidator,
  getContactUsListValidator,
  exportContactUsValidator,
  bulkDeleteContactUsValidator,
};
