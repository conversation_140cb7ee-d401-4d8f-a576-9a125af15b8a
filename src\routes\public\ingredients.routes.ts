import express from "express";
import ingredientsController from "../../controller/ingredients.controller";
import ingredientsValidator from "../../validators/ingredients.validator";

const router = express.Router();

/**
 * @swagger
 * /public/ingredients:
 *   get:
 *     tags:
 *       - Public Ingredients
 *     summary: Get all active ingredients
 *     description: Retrieve all active ingredients with their categories, nutrition, allergen, and dietary attributes. Includes filtering, searching, and pagination. No authentication required.
 *     parameters:
 *       - name: page
 *         in: query
 *         description: Page number for pagination
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - name: size
 *         in: query
 *         description: Number of items per page
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *       - name: search
 *         in: query
 *         description: Search term for ingredient name or description
 *         required: false
 *         schema:
 *           type: string
 *       - name: category
 *         in: query
 *         description: Filter by category name or ID
 *         required: false
 *         schema:
 *           type: string
 *       - name: allergy
 *         in: query
 *         description: Filter by allergen attribute
 *         required: false
 *         schema:
 *           type: string
 *       - name: cuisine
 *         in: query
 *         description: Filter by cuisine attribute
 *         required: false
 *         schema:
 *           type: string
 *       - name: dietary
 *         in: query
 *         description: Filter by dietary attribute
 *         required: false
 *         schema:
 *           type: string
 *       - name: sort_by
 *         in: query
 *         description: Sort field
 *         required: false
 *         schema:
 *           type: string
 *           enum: [ingredient_name, cost_per_unit, created_at, updated_at]
 *           default: ingredient_name
 *       - name: sort_order
 *         in: query
 *         description: Sort order
 *         required: false
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: ASC
 *     responses:
 *       200:
 *         description: Ingredients retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ingredients fetched successfully"
 *                 count:
 *                   type: integer
 *                   example: 150
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       ingredient_name:
 *                         type: string
 *                         example: "Tomato"
 *                       ingredient_description:
 *                         type: string
 *                         example: "Fresh red tomatoes"
 *                       ingredient_slug:
 *                         type: string
 *                         example: "tomato"
 *                       ingredient_status:
 *                         type: string
 *                         enum: [active]
 *                         example: "active"
 *                       cost_per_unit:
 *                         type: number
 *                         format: float
 *                         example: 2.50
 *                       waste_percentage:
 *                         type: number
 *                         format: float
 *                         example: 5.0
 *                       unit:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           unit_title:
 *                             type: string
 *                             example: "kg"
 *                           unit_description:
 *                             type: string
 *                             example: "Kilogram"
 *                       categories:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: integer
 *                               example: 1
 *                             category_name:
 *                               type: string
 *                               example: "Vegetables"
 *                             category_slug:
 *                               type: string
 *                               example: "vegetables"
 *                       nutrition_attributes:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: integer
 *                               example: 1
 *                             attribute_title:
 *                               type: string
 *                               example: "Vitamin C"
 *                             attribute_slug:
 *                               type: string
 *                               example: "vitamin-c"
 *                       allergy_attributes:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: integer
 *                               example: 2
 *                             attribute_title:
 *                               type: string
 *                               example: "None"
 *                             attribute_slug:
 *                               type: string
 *                               example: "none"
 *                       cuisine_attributes:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: integer
 *                               example: 3
 *                             attribute_title:
 *                               type: string
 *                               example: "Mediterranean"
 *                             attribute_slug:
 *                               type: string
 *                               example: "mediterranean"
 *                       dietary_attributes:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: integer
 *                               example: 4
 *                             attribute_title:
 *                               type: string
 *                               example: "Vegan"
 *                             attribute_slug:
 *                               type: string
 *                               example: "vegan"
 *                       conversions:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: integer
 *                               example: 1
 *                             from_unit:
 *                               type: string
 *                               example: "kg"
 *                             to_unit:
 *                               type: string
 *                               example: "g"
 *                             conversion_factor:
 *                               type: number
 *                               format: float
 *                               example: 1000
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-01-15T10:30:00Z"
 *                       updated_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-01-15T10:30:00Z"
 *                 page:
 *                   type: integer
 *                   example: 1
 *                 size:
 *                   type: integer
 *                   example: 10
 *                 total_pages:
 *                   type: integer
 *                   example: 15
 *       400:
 *         description: Bad request - validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Validation failed"
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["Invalid page number"]
 *       429:
 *         description: Too many requests
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Rate limit exceeded"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
router.get(
  "/get-all",
  ingredientsValidator.getIngredientsListValidator(),
  (req: any, res: any, next: any) => {
    // For public API, simulate a user with default access to bypass organization restrictions
    req.user = {
      id: null,
      organization_id: null,
      roles: [{ role_name: 'public' }]
    };
    // Force status to 'active' for public API
    req.query.ingredient_status = 'active';
    // Remove any organization filters for public access
    delete req.query.organization_id;
    // Disable download functionality for public API
    delete req.query.download;
    next();
  },
  ingredientsController.getIngredients
);
export default router;
