


export const ROLE_CONSTANT = Object.freeze({
  SUPER_ADMIN: "Super Admin",
  ADMIN: "Admin",
  DIRECTOR: "Director",
  HR: "HR",
  AREA_MANAGER: "Area Manager",
  ACCOUNTANT: "Accountant",
  <PERSON><PERSON><PERSON>_MANAGER: "Branch Manager",
  AS<PERSON><PERSON><PERSON>_BRANCH_MANAGER: "Assist. Branch Manager",
  HEAD_CHEF: "Head Chef",
  BAR_MANAGER: "Bar Manager",
  FOH: "FOH",
  BAR: "Bar",
  KITCH<PERSON>: "Kitchen",
  HOTEL_MANAGER: "Hotel Manager",
  ASSIGN_HOTEL_MANAGER: "Assist. Hotel Manager",
  RECEPTIONIST: "Receptionist",
  HEAD_HOUSEKEEPER: "Head Housekeeper",
  HOUSE_KEEPER: "House Keeper",
  SIGNATURE: "Signature",
});

export const ADMIN_SIDE_USER = [
  ROLE_CONSTANT.SUPER_ADMIN,
  ROLE_CONSTANT.ADMIN,
  ROLE_CONSTANT.DIRECTOR,
  R<PERSON><PERSON>_CONSTANT.ACCOUNTANT,
  ROLE_CONSTANT.HR,
  ROLE_CONSTANT.AREA_MANAGER,
  ROLE_CONSTANT.BRANCH_MANAGER,
  ROLE_CONSTANT.HOTEL_MANAGER,
  ROLE_CONSTANT.SIGNATURE,
];

export const NORMAL_USER = [
  ROLE_CONSTANT.SUPER_ADMIN,
  ROLE_CONSTANT.ADMIN,
  ROLE_CONSTANT.BRANCH_MANAGER,
  ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER,
  ROLE_CONSTANT.HEAD_CHEF,
  ROLE_CONSTANT.BAR_MANAGER,
  ROLE_CONSTANT.FOH,
  ROLE_CONSTANT.BAR,
  ROLE_CONSTANT.KITCHEN,
  ROLE_CONSTANT.HOTEL_MANAGER,
  ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER,
  ROLE_CONSTANT.RECEPTIONIST,
  ROLE_CONSTANT.HEAD_HOUSEKEEPER,
  ROLE_CONSTANT.HOUSE_KEEPER,
];

export const RABBITMQ_QUEUE = Object.freeze({
  STAFF_CREATION_DETAILS: 'staff_creation_details',
  STAFF_CREATION_SUCCESS: 'staff_creation_success',
  ORG_MASTER_USER: 'org_master_user',
  ORG_MASTER_USER_VERIFICATION_SUCCESS: 'org_master_user_verification_success',
  STAFF_CREATION: 'staff_creation',
  STAFF_PASSWORD_PIN_GENERATE: 'staff_password_pin_generate',
  SESSION_STORE: 'session_store',
  USER_ACTIVITY_LOG: 'user_activity_log',
  BANNER_NOTIFICATION: 'banner_notification',
  EMAIL_NOTIFICATION: 'email_notification',
  PUSH_NOTIFICATION_SUCCESS: 'push_notification_success'
})