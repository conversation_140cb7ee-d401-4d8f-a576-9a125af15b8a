const { QueryTypes } = require("sequelize");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    // Check if HACCP attributes already exist
    const existingHACCP = await queryInterface.sequelize.query(
      `SELECT * FROM mo_food_attributes WHERE organization_id IS NULL AND is_system_attribute = true AND attribute_type = 'haccp_category'`,
      { type: QueryTypes.SELECT }
    );

    if (existingHACCP.length === 0) {

      // HACCP Category Attributes
      const haccpAttributes = [
        {
          name: "General",
          slug: "general",
          description: "General HACCP procedures including purchase, receipt/delivery, and collect operations"
        },
        {
          name: "Purchase",
          slug: "purchase",
          description: "HACCP procedures for purchasing ingredients and supplies"
        },
        {
          name: "Receipt / Delivery",
          slug: "receipt-delivery",
          description: "HACCP procedures for receiving and delivery of ingredients"
        },
        {
          name: "Collect",
          slug: "collect",
          description: "HACCP procedures for collection of ingredients and materials"
        },
        {
          name: "Storage",
          slug: "storage",
          description: "HACCP procedures for proper storage of ingredients and prepared foods"
        },
        {
          name: "Preparation",
          slug: "preparation",
          description: "HACCP procedures for food preparation and handling"
        },
        {
          name: "Cooking",
          slug: "cooking",
          description: "HACCP procedures for cooking processes and temperature control"
        },
        {
          name: "Hot Holding / Reheating",
          slug: "hot-holding-reheating",
          description: "HACCP procedures for hot holding and reheating of cooked foods"
        },
        {
          name: "Cooling",
          slug: "cooling",
          description: "HACCP procedures for cooling down hot foods safely"
        },
        {
          name: "Allergens",
          slug: "allergens",
          description: "HACCP procedures for allergen control and management"
        }
      ];

      // Prepare bulk insert data
      const haccpData = haccpAttributes.map(haccp => ({
        attribute_title: haccp.name,
        attribute_slug: haccp.slug,
        attribute_description: haccp.description,
        attribute_type: "haccp_category",
        attribute_icon: null,
        attribute_status: "active",
        organization_id: null,
        is_system_attribute: true,
        created_by: 1,
        updated_by: 1,
        created_at: new Date(),
        updated_at: new Date()
      }));

      await queryInterface.bulkInsert("mo_food_attributes", haccpData);
      console.log("✅ HACCP Category Attributes seeded successfully");
    } else {
      console.log("⏭️  HACCP Category Attributes already exist, skipping...");
    }
  },

  async down(queryInterface) {
    await queryInterface.bulkDelete("mo_food_attributes", {
      organization_id: null,
      is_system_attribute: true,
      attribute_type: "haccp_category"
    });
  }
};
