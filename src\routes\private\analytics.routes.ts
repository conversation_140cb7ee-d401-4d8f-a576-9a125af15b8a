import express from "express";
import analyticsController from "../../controller/analytics.controller";
import analyticsValidator from "../../validators/analytics.validator";

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     AnalyticsEvent:
 *       type: object
 *       properties:
 *         event_type:
 *           type: string
 *           enum: [recipe_view, cta_click, contact_form_submit, recipe_download, recipe_share, dashboard_view]
 *         entity_type:
 *           type: string
 *           enum: [recipe, category, ingredient, dashboard, settings]
 *         entity_id:
 *           type: number
 *         session_id:
 *           type: string
 *         metadata:
 *           type: object
 *       required:
 *         - event_type
 *         - entity_type
 */

/**
 * @swagger
 * /v1/private/analytics:
 *   get:
 *     summary: Get analytics summary
 *     tags: [Analytics]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 20
 *         description: Number of items per page
 *       - in: query
 *         name: event_type
 *         schema:
 *           type: string
 *           enum: [recipe_view, cta_click, contact_form_submit]
 *         description: Filter by event type
 *       - in: query
 *         name: entity_type
 *         schema:
 *           type: string
 *           enum: [recipe, category, ingredient]
 *         description: Filter by entity type
 *       - in: query
 *         name: entity_id
 *         schema:
 *           type: integer
 *         description: Filter by specific entity ID
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for filtering (ISO format)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for filtering (ISO format)
 *     responses:
 *       200:
 *         description: Analytics summary retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Analytics summary fetched successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     events:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/AnalyticsEvent'
 *                     pagination:
 *                       $ref: '#/components/schemas/Pagination'
 *       500:
 *         description: Internal server error
 */
router.get(
  "/",
  analyticsValidator.getAnalyticsSummaryValidator(),
  analyticsController.getAnalyticsSummary
);

// Analytics Summary endpoint - Essential for basic analytics

/**
 * @swagger
 * /v1/private/analytics/cta-clicks:
 *   get:
 *     summary: Get CTA click analytics
 *     tags: [Analytics]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: date_range
 *         schema:
 *           type: string
 *           enum: [last_7_days, last_30_days, last_90_days, last_year]
 *           default: last_30_days
 *         description: Date range for analytics
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort order by clicks
 *     responses:
 *       200:
 *         description: CTA analytics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       recipe_id:
 *                         type: number
 *                       recipe_name:
 *                         type: string
 *                       cta_type:
 *                         type: string
 *                       clicks:
 *                         type: number
 *                       last_clicked_at:
 *                         type: string
 *                         format: date-time
 *       500:
 *         description: Internal server error
 */
/**
 * Get CTA click analytics (Left side 50% of dashboard)
 * Shows: Recipe Name, CTA Type, Clicks, Last Clicked At
 */
router.get(
  "/cta-clicks",
  analyticsValidator.getCtaClickAnalyticsValidator(),
  analyticsController.getCtaClickAnalytics
);

/**
 * @swagger
 * /v1/private/analytics/contact-submissions:
 *   get:
 *     summary: Get contact form submission analytics
 *     tags: [Analytics]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: date_range
 *         schema:
 *           type: string
 *           enum: [last_7_days, last_30_days, last_90_days, last_year]
 *           default: last_30_days
 *         description: Date range for analytics
 *       - in: query
 *         name: recipe_id
 *         schema:
 *           type: number
 *         description: Filter by specific recipe ID
 *     responses:
 *       200:
 *         description: Contact submission analytics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       recipe_id:
 *                         type: number
 *                       recipe_name:
 *                         type: string
 *                       contact_name:
 *                         type: string
 *                       contact_email:
 *                         type: string
 *                       contact_mobile:
 *                         type: string
 *                       message:
 *                         type: string
 *                       submitted_on:
 *                         type: string
 *                         format: date-time
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /v1/private/analytics/recipe-views:
 *   get:
 *     summary: Get recipe view analytics
 *     tags: [Analytics]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: date_range
 *         schema:
 *           type: string
 *           enum: [last_7_days, last_30_days, last_90_days, last_year, custom]
 *           default: last_30_days
 *         description: Date range for analytics
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date (required when date_range=custom)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: End date (required when date_range=custom)
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort order by views
 *     responses:
 *       200:
 *         description: Recipe view analytics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe view analytics fetched successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       recipe_id:
 *                         type: number
 *                         example: 123
 *                       recipe_name:
 *                         type: string
 *                         example: "Chocolate Cake"
 *                       total_views:
 *                         type: number
 *                         example: 150
 *                       recent_views:
 *                         type: number
 *                         example: 25
 *                       last_viewed:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-01-15T10:30:00Z"
 *                       avg_view_duration:
 *                         type: number
 *                         example: 45.5
 *                       unique_sessions:
 *                         type: number
 *                         example: 20
 *       500:
 *         description: Internal server error
 */
router.get(
  "/recipe-views",
  analyticsValidator.getRecipeViewAnalyticsValidator(),
  analyticsController.getRecipeViewAnalytics
);

/**
 * Get contact form submissions (Right side 50% of dashboard)
 * Shows: Recipe Name, Name, Email, Mobile, Message, Submitted On
 */
router.get(
  "/contact-submissions",
  analyticsValidator.getContactSubmissionsValidator(),
  analyticsController.getContactSubmissionAnalytics
);

/**
 * Export contact form submissions to CSV or JSON
 * @route GET /api/v1/private/analytics/contact-submissions/export
 */
router.get(
  "/contact-submissions/export",
  analyticsValidator.exportContactSubmissionsValidator(),
  analyticsController.exportContactSubmissions
);

/**
 * @swagger
 * /v1/private/analytics/contact-submissions/{id}:
 *   delete:
 *     summary: Delete contact form submission
 *     tags: [Analytics]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Contact submission ID to delete
 *     responses:
 *       200:
 *         description: Contact submission deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Contact submission deleted successfully"
 *       404:
 *         description: Contact submission not found
 *       500:
 *         description: Internal server error
 */
router.delete(
  "/contact-submissions/:id",
  analyticsValidator.deleteContactSubmissionValidator(),
  analyticsController.deleteContactSubmission
);

/**
 * Bulk delete contact form submissions from analytics
 * @route DELETE /api/v1/private/analytics/contact-submissions/bulk-delete
 */
router.delete(
  "/contact-submissions/bulk-delete",
  analyticsValidator.bulkDeleteContactSubmissionsValidator(),
  analyticsController.bulkDeleteContactSubmissions
);

/**
 * @swagger
 * /v1/private/analytics/recipe-view-statistics/{recipeId}:
 *   get:
 *     summary: Get recipe view statistics for private recipes with assigned users
 *     tags: [Analytics]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: recipeId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Recipe ID to get view statistics for
 *       - in: query
 *         name: organization_id
 *         schema:
 *           type: string
 *         description: Organization ID (optional for admin users)
 *     responses:
 *       200:
 *         description: Recipe view statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe view statistics retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       user_id:
 *                         type: integer
 *                         example: 123
 *                       user_full_name:
 *                         type: string
 *                         example: "John Doe"
 *                       user_email:
 *                         type: string
 *                         example: "<EMAIL>"
 *                       user_avatar:
 *                         type: string
 *                         example: "https://api.example.com/uploads/avatar.jpg"
 *                       user_branch:
 *                         type: string
 *                         example: "Main Branch"
 *                       user_department:
 *                         type: string
 *                         example: "Kitchen"
 *                       last_recipe_view:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-01-15T10:30:00Z"
 *                       total_view_count:
 *                         type: integer
 *                         example: 15
 *       400:
 *         description: Bad request - Recipe not private, no assigned users, or invalid recipe ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Recipe view statistics are only available for private recipes"
 *       404:
 *         description: Recipe not found
 *       500:
 *         description: Internal server error
 */
router.get(
  "/recipe-view-statistics/:recipeId",
  analyticsValidator.getRecipeViewStatisticsValidator(),
  analyticsController.getRecipeViewStatistics
);

/**
 * @swagger
 * /v1/private/analytics/reset-view-statistics/{recipeId}:
 *   delete:
 *     summary: Reset recipe view statistics for private recipes with assigned users
 *     tags: [Analytics]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: recipeId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Recipe ID to reset view statistics for
 *       - in: query
 *         name: organization_id
 *         schema:
 *           type: string
 *         description: Organization ID (optional for admin users)
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user_ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                   minimum: 1
 *                 description: Optional array of user IDs to reset statistics for. If not provided, resets all statistics for the recipe.
 *                 example: [123, 456, 789]
 *     responses:
 *       200:
 *         description: Recipe view statistics reset successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe view statistics reset successfully for specified users"
 *       400:
 *         description: Bad request - Recipe not private, no assigned users, invalid recipe ID, or invalid user IDs
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Recipe view statistics reset is only available for private recipes"
 *       404:
 *         description: Recipe not found
 *       500:
 *         description: Internal server error
 */
router.delete(
  "/reset-view-statistics/:recipeId",
  analyticsValidator.resetRecipeViewStatisticsValidator(),
  analyticsController.resetRecipeViewStatistics
);

// Clean analytics routes - only essential endpoints

export default router;
